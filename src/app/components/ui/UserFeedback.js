import { getTranslation } from '@/lib/i18n';
import { Card, CardBody } from '@heroui/react';

export default function UserFeedback({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const userFeedbacks = [
    {
      name: '<PERSON>',
      title: t('DIY Enthusiast'),
      description: t('<PERSON>, a DIY enthusiast, uses our string art generator free to create personalized wall art for her living room. By uploading a family portrait, she generates a pattern with 288 pins and 4000 lines, downloading a TXT sequence to craft a heartfelt piece that adds warmth to her home.'),
    },
    {
      name: 'Mr. <PERSON>',
      title: t('Math Teacher'),
      description: t('<PERSON><PERSON>, a high school math teacher, leverages our string art generator online to teach geometric concepts. He uploads a parabola image, adjusts the line weight to 20, and uses the generated pattern to show students how curves form through linear connections, making math engaging.'),
    },
    {
      name: '<PERSON>',
      title: t('Graphic Designer'),
      description: t("<PERSON>, a graphic designer, uses our string art generator to design a unique logo for a client's trade show booth.She uploads the company's logo, customizes the pattern, and exports an SVG file for professional printing, enhancing the brand's visual impact."),
    },
    {
      name: 'Event Planner',
      title: t('<PERSON>'),
      description: t("<PERSON>, an event planner, creates custom string art centerpieces for a wedding using our free string art generator online. He uploads a couple's monogram, generates a pattern, and crafts it into a stunning table decoration, delighting his clients with a personalized touch."),
    },
    {
      name: 'Raj',
      title: t('CNC Operator'),
      description: t("Raj, a CNC operator, relies on our string art generator free online to streamline production. He uploads a client's design, exports the SVG file with 288 pins and 4000 lines, and uses it directly in his CNC machine, saving time and ensuring precision."),
    },
    {
      name: 'Mia',
      title: t('Artist'),
      description: t('Mia, a contemporary artist, uses our string art generator to create intricate patterns for her gallery show. By experimenting with high-contrast abstract images, she generates unique designs that blend technology and craftsmanship, captivating her audience.'),
    },
  ]

  return (
    <>
      <h3 className="text-2xl font-bold px-2 py-4">{t('User Reviews of Our String Art Converter')}</h3>
      <div className="flex flex-wrap gap-8 justify-between">
        {userFeedbacks.map((feedback, index) => (
          <Card
            shadow="none"
            disableRipple
            className="select-none box-border border-foreground/10 border-[1px] min-w-[160px] max-w-full md:max-w-[30%] p-2 flex-shrink-0"
            radius="lg"
            key={index}
          >
            <CardBody>
              <p>{feedback.description}</p>
              <p className="text-sm mt-8">{feedback.name}</p>
              <p className="text-sm text-gray-500 mt-2">{feedback.title}</p>
            </CardBody>
          </Card>
        ))}
      </div>
    </>
  )
}
