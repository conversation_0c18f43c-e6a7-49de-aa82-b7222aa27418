'use client';
import { Accordion, AccordionItem } from "@heroui/react";
import { getTranslation } from '@/lib/i18n';

export default function FAQ({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const faq = [
    {
      label: "What is a string art generator?",
      question: t("What is a string art generator?"),
      answer: t("A string art generator is an online tool that converts images into string art patterns by calculating nail and thread connections to recreate the image."),
    },
    {
      label: "Is String Art Generator free?",
      question: t("Is String Art Generator free?"),
      answer: t("Yes, our string art generator free provides all core features at no cost, with no subscriptions or hidden fees."),
    },
    {
      label: "What image formats are supported?",
      question: t("What image formats are supported?"),
      answer: t("You can upload JPEG, PNG, or WEBP images to our string art generator online for conversion."),
    },
    {
      label: "Do I need coding skills to use String Art Generator?",
      question: t("Do I need coding skills to use String Art Generator?"),
      answer: t("No, our tool is user-friendly, requiring no technical expertise to create stunning string art."),
    },
    {
      label: "What are the best images for string art?",
      question: t("What are the best images for string art?"),
      answer: t("High-contrast images, like black-and-white portraits or simple logos, work best with our free string art generator online."),
    },
    {
      label: "Can I customize my string art pattern?",
      question: t("Can I customize my string art pattern?"),
      answer: t("Yes, adjust the number of pins (default 288), lines (default 4000), and line weight (default 20) to create a unique pattern."),
    },
    {
      label: "How long does it take to generate a pattern?",
      question: t("How long does it take to generate a pattern?"),
      answer: t("Generation takes a few seconds to a minute, depending on image complexity and settings."),
    },
    {
      label: "What are the output formats?",
      question: t("What are the output formats?"),
      answer: t("Our string art generator provides TXT files for nail sequences and SVG files for printing or CNC use."),
    },
    {
      label: "Can I use the patterns for physical crafting?",
      question: t("Can I use the patterns for physical crafting?"),
      answer: t("Yes, download the TXT sequence to guide nail and thread placement on a physical board."),
    },
    {
      label: "Is String Art Generator mobile-friendly?",
      question: t("Is String Art Generator mobile-friendly?"),
      answer: t("Yes, our string art generator online is fully responsive, working seamlessly on desktops, tablets, and smartphones."),
    },
  ]

  return (
    <>
      <h3 className="text-2xl font-bold px-2 py-4">{t('Frequently Asked Questions about Unix Timestamp Converter')}</h3>
      < Accordion
        selectionMode="multiple"
        className="border-foreground/10 border-[1px] rounded-2xl px-6"
      >
        {
          faq.map((item, index) => (
            <AccordionItem key={index} aria-label={item.label} title={item.question}>
              {item.answer}
            </AccordionItem>
          ))
        }
      </Accordion>
    </>
  )
}
