import { getTranslation } from '@/lib/i18n';
import FAQ from '@/app/components/ui/FAQ';
import Hero from '@/app/components/ui/Hero';
import KeyFeatures from '@/app/components/ui/KeyFeatures';
import UserFeedback from '@/app/components/ui/UserFeedback';
import FeaturedOn from '@/app/components/ui/FeaturedOn';
import { ToastProvider } from '@heroui/react';
import WhatIs from '../components/ui/WhatIs';
import HowToUse from '../components/ui/HowToUse';

export default async function Home({ params: { locale } }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  return (
    <>
      <ToastProvider placement="top-center" toastOffset={230} />
      <div className="page-container">
        <div id="hero" className="section">
          <Hero locale={locale} />
        </div>
        <div id="what-is" className="section bg-gray-100 dark:bg-gray-800 rounded mt-20">
          <WhatIs locale={locale}></WhatIs>
        </div>
        <div id="key-features" className="section mt-10">
          <KeyFeatures locale={locale} />
        </div>
        <div id="how-to-use" className="section bg-gray-100 dark:bg-gray-800 rounded mt-20">
          <HowToUse locale={locale}></HowToUse>
        </div>
        <div id="use-case" className="section mt-10">
          <UserFeedback locale={locale} />
        </div>
        <div id="faq" className="section">
          <FAQ locale={locale} />
        </div>
        <div className="section">
          <FeaturedOn />
        </div>
      </div>
    </>
  );
}
